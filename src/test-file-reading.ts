import fs from "fs";
import path from "path";
import { csvUtils } from "./modules/core/utils/csvUtils";
import { binanceUtils } from "./modules/core/utils/binanceUtils";

async function testFileReading() {
  try {
    console.log("🔍 Testing file reading simulation...");
    
    // Read the CSV file as the browser would
    const csvPath = path.join(__dirname, "../data-ai/binance_card_2022 - sheet1.csv");
    const fileBuffer = fs.readFileSync(csvPath);
    
    console.log(`📁 File size: ${fileBuffer.length} bytes`);
    
    // Convert to string as browser would do with file.text()
    const csvContent = fileBuffer.toString('utf-8');
    console.log(`📝 Content length: ${csvContent.length} characters`);
    
    // Test basic CSV parsing
    console.log("\n🔍 Testing basic CSV parsing...");
    const parseResult = csvUtils.parseCSV(csvContent);
    if (parseResult.isErr()) {
      console.error("❌ Basic CSV parsing failed:", parseResult.error);
      return;
    }
    
    console.log(`✅ Basic parsing: ${parseResult.value.rowCount} rows`);
    
    // Test Binance CSV parsing
    console.log("\n🔍 Testing Binance CSV parsing...");
    const binanceResult = csvUtils.parseBinanceCSV(csvContent);
    if (binanceResult.isErr()) {
      console.error("❌ Binance CSV parsing failed:", binanceResult.error);
      return;
    }
    
    console.log(`✅ Binance parsing: ${binanceResult.value.length} transactions`);
    
    // Test transaction processing
    console.log("\n🔍 Testing transaction processing...");
    const processResult = binanceUtils.processBinanceTransactions(binanceResult.value, "Test Account");
    if (processResult.isErr()) {
      console.error("❌ Transaction processing failed:", processResult.error);
      return;
    }
    
    console.log(`✅ Processing: ${processResult.value.length} processed transactions`);
    
    // Test with different encodings
    console.log("\n🔍 Testing different encodings...");
    const contentLatin1 = fileBuffer.toString('latin1');
    const contentAscii = fileBuffer.toString('ascii');
    
    console.log(`📝 UTF-8 length: ${csvContent.length}`);
    console.log(`📝 Latin1 length: ${contentLatin1.length}`);
    console.log(`📝 ASCII length: ${contentAscii.length}`);
    
    // Check if there are any null bytes or special characters
    const nullBytes = csvContent.split('\0').length - 1;
    const nonPrintable = csvContent.replace(/[\x20-\x7E\r\n\t]/g, '').length;
    
    console.log(`🔍 Null bytes: ${nullBytes}`);
    console.log(`🔍 Non-printable chars: ${nonPrintable}`);
    
    // Check line endings
    const crlfCount = (csvContent.match(/\r\n/g) || []).length;
    const lfCount = (csvContent.match(/(?<!\r)\n/g) || []).length;
    const crCount = (csvContent.match(/\r(?!\n)/g) || []).length;
    
    console.log(`📝 Line endings - CRLF: ${crlfCount}, LF: ${lfCount}, CR: ${crCount}`);
    
    // Show first few lines
    const lines = csvContent.split(/\r?\n/);
    console.log(`📝 Total lines: ${lines.length}`);
    console.log("📝 First 3 lines:");
    lines.slice(0, 3).forEach((line, i) => {
      console.log(`  ${i + 1}: ${line.substring(0, 100)}${line.length > 100 ? '...' : ''}`);
    });
    
    console.log("📝 Last 3 lines:");
    lines.slice(-3).forEach((line, i) => {
      const lineNum = lines.length - 3 + i + 1;
      console.log(`  ${lineNum}: ${line.substring(0, 100)}${line.length > 100 ? '...' : ''}`);
    });
    
  } catch (error) {
    console.error("❌ Test failed:", error);
  }
}

// Run the test
testFileReading().then(() => {
  console.log("\n🎉 File reading test completed!");
}).catch(error => {
  console.error("💥 Test crashed:", error);
});
