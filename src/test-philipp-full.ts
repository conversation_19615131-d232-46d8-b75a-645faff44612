import fs from "fs";
import path from "path";
import { csvUtils } from "./modules/core/utils/csvUtils";
import { binanceUtils } from "./modules/core/utils/binanceUtils";

async function testPhilippFull() {
  console.log("🔍 Testing full processing pipeline with Philipp's CSV...");
  
  try {
    const csvPath = path.join(__dirname, "../data-ai/philipp-binance - Sheet1.csv");
    const csvContent = fs.readFileSync(csvPath, 'utf-8');
    
    console.log(`📁 File: ${csvContent.length} characters`);
    
    // Parse Binance CSV
    const parseResult = csvUtils.parseBinanceCSV(csvContent);
    if (parseResult.isErr()) {
      console.error("❌ Parse failed:", parseResult.error);
      return;
    }
    
    console.log(`✅ Parsed: ${parseResult.value.length} transactions`);
    
    // Process transactions
    const processResult = binanceUtils.processBinanceTransactions(parseResult.value, "Philipp Binance Card");
    if (processResult.isErr()) {
      console.error("❌ Process failed:", processResult.error);
      return;
    }
    
    const processed = processResult.value;
    console.log(`✅ Processed: ${processed.length} transactions`);
    
    // Show summary
    const totalTrades = processed.reduce((sum, t) => sum + t.trades.length, 0);
    console.log(`📊 Summary: ${processed.length} transfers, ${totalTrades} trades`);
    
    // Calculate totals
    const totalOut = processed
      .filter(t => t.transfer.from === "Philipp Binance Card")
      .reduce((sum, t) => sum + parseFloat(t.transfer.amount.toString()), 0);
    
    const totalIn = processed
      .filter(t => t.transfer.to === "Philipp Binance Card")
      .reduce((sum, t) => sum + parseFloat(t.transfer.amount.toString()), 0);
    
    console.log(`💰 Total Out: €${totalOut.toFixed(2)}`);
    console.log(`💰 Total In: €${totalIn.toFixed(2)}`);
    console.log(`💰 Net: €${(totalIn - totalOut).toFixed(2)}`);
    
    // Show sample transactions
    console.log("\n📝 Sample transactions:");
    processed.slice(0, 5).forEach((tx, i) => {
      console.log(`  ${i + 1}. ${tx.transfer.description} - €${tx.transfer.amount} (${tx.trades.length} trades)`);
    });
    
    // Show currencies involved
    const currencies = new Set(processed.flatMap(t => [
      t.transfer.currencyCode,
      ...t.trades.flatMap(trade => [trade.tokenFrom, trade.tokenTo])
    ]));
    console.log(`🪙 Currencies: ${Array.from(currencies).join(", ")}`);
    
    console.log("\n✅ Full pipeline test successful!");
    
  } catch (error) {
    console.error("❌ Test failed:", error);
  }
}

testPhilippFull();
