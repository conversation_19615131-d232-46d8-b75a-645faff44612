import fs from "fs";
import path from "path";

async function testAPIUpload() {
  try {
    console.log("🔍 Testing API upload simulation...");
    
    // Read the CSV file
    const csvPath = path.join(__dirname, "../data-ai/binance_card_2022 - sheet1.csv");
    const csvContent = fs.readFileSync(csvPath, 'utf-8');
    
    console.log(`📁 File content: ${csvContent.length} characters`);
    
    // Test 1: Direct tRPC call simulation
    console.log("\n🔍 Testing direct API call...");
    
    const payload = {
      fileContent: csvContent,
      fileName: "test-binance.csv",
      csvType: "binance-card"
    };
    
    const payloadSize = JSON.stringify(payload).length;
    console.log(`📦 Payload size: ${payloadSize} bytes (${(payloadSize / 1024).toFixed(2)} KB)`);
    
    // Test 2: Simulate HTTP request
    console.log("\n🔍 Testing HTTP request simulation...");
    
    try {
      const response = await fetch('http://localhost:3001/api/trpc/csv.uploadAndAnalyze', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          json: payload
        })
      });
      
      console.log(`📡 Response status: ${response.status}`);
      console.log(`📡 Response headers:`, Object.fromEntries(response.headers.entries()));
      
      if (response.ok) {
        const data = await response.json();
        console.log(`✅ Upload successful:`, data);
      } else {
        const errorText = await response.text();
        console.log(`❌ Upload failed:`, errorText);
      }
    } catch (error) {
      console.error("❌ HTTP request failed:", error);
    }
    
    // Test 3: Test with truncated content
    console.log("\n🔍 Testing with truncated content...");
    
    const truncatedContent = csvContent.substring(0, 2000); // First 2KB
    const truncatedPayload = {
      fileContent: truncatedContent,
      fileName: "test-truncated.csv",
      csvType: "binance-card"
    };
    
    console.log(`📝 Truncated content: ${truncatedContent.length} characters`);
    
    try {
      const response = await fetch('http://localhost:3001/api/trpc/csv.uploadAndAnalyze', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          json: truncatedPayload
        })
      });
      
      console.log(`📡 Truncated response status: ${response.status}`);
      
      if (response.ok) {
        const data = await response.json();
        console.log(`✅ Truncated upload successful:`, data);
      } else {
        const errorText = await response.text();
        console.log(`❌ Truncated upload failed:`, errorText);
      }
    } catch (error) {
      console.error("❌ Truncated HTTP request failed:", error);
    }
    
    // Test 4: Check if the issue is with specific content
    console.log("\n🔍 Testing content analysis...");
    
    const lines = csvContent.split('\n');
    console.log(`📝 Total lines: ${lines.length}`);
    
    // Test with first 22 lines (the problematic number)
    const first22Lines = lines.slice(0, 23).join('\n'); // +1 for header
    const first22Payload = {
      fileContent: first22Lines,
      fileName: "test-first22.csv",
      csvType: "binance-card"
    };
    
    console.log(`📝 First 22 lines: ${first22Lines.length} characters`);
    
    try {
      const response = await fetch('http://localhost:3001/api/trpc/csv.uploadAndAnalyze', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          json: first22Payload
        })
      });
      
      console.log(`📡 First 22 lines response status: ${response.status}`);
      
      if (response.ok) {
        const data = await response.json();
        console.log(`✅ First 22 lines upload successful:`, data);
      } else {
        const errorText = await response.text();
        console.log(`❌ First 22 lines upload failed:`, errorText);
      }
    } catch (error) {
      console.error("❌ First 22 lines HTTP request failed:", error);
    }
    
  } catch (error) {
    console.error("❌ Test failed:", error);
  }
}

// Run the test
testAPIUpload().then(() => {
  console.log("\n🎉 API upload test completed!");
}).catch(error => {
  console.error("💥 Test crashed:", error);
});
