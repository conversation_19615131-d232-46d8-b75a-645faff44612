import fs from "fs";
import path from "path";
import { csvUtils } from "./modules/core/utils/csvUtils";

async function testContentIssues() {
  try {
    console.log("🔍 Testing potential content issues...");
    
    // Read the CSV file
    const csvPath = path.join(__dirname, "../data-ai/binance_card_2022 - sheet1.csv");
    const fileBuffer = fs.readFileSync(csvPath);
    const csvContent = fileBuffer.toString('utf-8');
    
    console.log(`📁 Original file: ${csvContent.length} characters`);
    
    // Test 1: Check for BOM (Byte Order Mark)
    const hasBOM = csvContent.charCodeAt(0) === 0xFEFF;
    console.log(`🔍 Has BOM: ${hasBOM}`);
    
    // Test 2: Check for different line ending patterns
    const lines = csvContent.split(/\r?\n/);
    console.log(`📝 Total lines: ${lines.length}`);
    
    // Test 3: Look for lines that might cause parsing issues
    let problematicLines = [];
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];
      
      // Check for lines with unbalanced quotes
      const quoteCount = (line.match(/"/g) || []).length;
      if (quoteCount % 2 !== 0) {
        problematicLines.push({ line: i + 1, issue: 'unbalanced quotes', content: line });
      }
      
      // Check for lines with unusual comma counts
      const commaCount = (line.match(/,/g) || []).length;
      if (i > 0 && commaCount !== 6) { // Should have 6 commas for 7 columns
        problematicLines.push({ line: i + 1, issue: `wrong comma count (${commaCount})`, content: line });
      }
      
      // Check for very long lines that might cause issues
      if (line.length > 1000) {
        problematicLines.push({ line: i + 1, issue: 'very long line', content: line.substring(0, 100) + '...' });
      }
    }
    
    if (problematicLines.length > 0) {
      console.log("⚠️ Found problematic lines:");
      problematicLines.forEach(p => {
        console.log(`  Line ${p.line}: ${p.issue} - ${p.content}`);
      });
    } else {
      console.log("✅ No obvious problematic lines found");
    }
    
    // Test 4: Try parsing with different Papa Parse configurations
    console.log("\n🔍 Testing different Papa Parse configurations...");
    
    const configs = [
      { name: "Default", config: { header: true, skipEmptyLines: true } },
      { name: "No skip empty", config: { header: true, skipEmptyLines: false } },
      { name: "With quotes", config: { header: true, skipEmptyLines: true, quoteChar: '"' } },
      { name: "With delimiter", config: { header: true, skipEmptyLines: true, delimiter: ',' } },
      { name: "Strict", config: { header: true, skipEmptyLines: true, skipEmptyLines: 'greedy' } },
    ];
    
    for (const { name, config } of configs) {
      try {
        const Papa = require('papaparse');
        const result = Papa.parse(csvContent, config);
        console.log(`  ${name}: ${result.data.length} rows, ${result.errors.length} errors`);
        if (result.errors.length > 0) {
          console.log(`    Errors: ${result.errors.map(e => e.message).join(', ')}`);
        }
      } catch (error) {
        console.log(`  ${name}: Failed - ${error.message}`);
      }
    }
    
    // Test 5: Check if the issue is at a specific line number (around line 22)
    console.log("\n🔍 Testing truncation at different points...");
    
    const testPoints = [20, 21, 22, 23, 24, 25, 30, 50];
    for (const point of testPoints) {
      const truncatedLines = lines.slice(0, point + 1); // +1 to include header
      const truncatedContent = truncatedLines.join('\n');
      
      try {
        const parseResult = csvUtils.parseCSV(truncatedContent);
        if (parseResult.isOk()) {
          console.log(`  At line ${point}: ${parseResult.value.rowCount} rows parsed successfully`);
        } else {
          console.log(`  At line ${point}: Parse failed - ${parseResult.error.message}`);
        }
      } catch (error) {
        console.log(`  At line ${point}: Exception - ${error.message}`);
      }
    }
    
    // Test 6: Check for encoding issues
    console.log("\n🔍 Testing encoding issues...");
    
    const encodings = ['utf8', 'latin1', 'ascii'];
    for (const encoding of encodings) {
      try {
        const content = fileBuffer.toString(encoding as any);
        const parseResult = csvUtils.parseCSV(content);
        if (parseResult.isOk()) {
          console.log(`  ${encoding}: ${parseResult.value.rowCount} rows`);
        } else {
          console.log(`  ${encoding}: Parse failed`);
        }
      } catch (error) {
        console.log(`  ${encoding}: Exception`);
      }
    }
    
    // Test 7: Simulate browser file reading
    console.log("\n🔍 Simulating browser file reading...");
    
    // Create a Blob and read it as text (similar to what browser does)
    const { Blob } = require('buffer');
    const blob = new Blob([fileBuffer]);
    
    // Simulate file.text() method
    const textContent = fileBuffer.toString('utf-8');
    console.log(`📝 Simulated file.text(): ${textContent.length} characters`);
    
    const parseResult = csvUtils.parseCSV(textContent);
    if (parseResult.isOk()) {
      console.log(`✅ Simulated browser reading: ${parseResult.value.rowCount} rows`);
    } else {
      console.log(`❌ Simulated browser reading failed: ${parseResult.error.message}`);
    }
    
  } catch (error) {
    console.error("❌ Test failed:", error);
  }
}

// Run the test
testContentIssues().then(() => {
  console.log("\n🎉 Content issues test completed!");
}).catch(error => {
  console.error("💥 Test crashed:", error);
});
