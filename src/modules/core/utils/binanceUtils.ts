import { Result, ok, err } from "neverthrow";
import { Decimal } from "@/prisma/generated/runtime/library";
import { csvUtils } from "./csvUtils";

export namespace binanceUtils {
  export interface ParsedAsset {
    currency: string;
    amount: number;
  }

  export interface ParsedExchangeRate {
    fromCurrency: string;
    toCurrency: string;
    rate: number;
  }

  export interface ProcessedTransaction {
    transfer: {
      from: string;
      to: string;
      counterparty: string;
      amount: Decimal;
      currencyCode: string;
      description: string;
      executedAt: Date;
    };
    trades: {
      tokenFrom: string;
      tokenTo: string;
      amountFrom: Decimal;
      amountTo: Decimal;
      description: string;
      poolId: string;
      executedAt: Date;
    }[];
  }

  export function parseAssetsUsed(assetsUsedStr: string): ParsedAsset[] {
    if (!assetsUsedStr.trim()) return [];

    const assets: ParsedAsset[] = [];

    // Split by semicolon for multiple assets
    const assetParts = assetsUsedStr.split(";").map((part) => part.trim());

    for (const part of assetParts) {
      // Match patterns like "EUR 29.17", "USDT 0.04115926", "BNB 0.00010402"
      const match = part.match(/([A-Z]+)\s+([\d.]+)/);
      if (match) {
        const [, currency, amountStr] = match;
        const amount = parseFloat(amountStr);
        if (!isNaN(amount)) {
          assets.push({ currency, amount });
        }
      }
    }

    return assets;
  }

  export function parseExchangeRates(exchangeRatesStr: string): ParsedExchangeRate[] {
    if (!exchangeRatesStr.trim()) return [];

    const rates: ParsedExchangeRate[] = [];

    // Split by semicolon for multiple rates
    const rateParts = exchangeRatesStr.split(";").map((part) => part.trim());

    for (const part of rateParts) {
      // Match patterns like "1.00 EUR = 1.02898150 USDT"
      const match = part.match(/([\d.]+)\s+([A-Z]+)\s+=\s+([\d.]+)\s+([A-Z]+)/);
      if (match) {
        const [, fromAmount, fromCurrency, toAmount, toCurrency] = match;
        const fromAmountNum = parseFloat(fromAmount);
        const toAmountNum = parseFloat(toAmount);

        if (!isNaN(fromAmountNum) && !isNaN(toAmountNum) && fromAmountNum > 0) {
          const rate = toAmountNum / fromAmountNum;
          rates.push({
            fromCurrency,
            toCurrency,
            rate,
          });
        }
      }
    }

    return rates;
  }

  export function processBinanceTransaction(
    transaction: csvUtils.BinanceTransaction,
    accountName: string
  ): Result<ProcessedTransaction, { type: "ProcessingError"; message: string }> {
    try {
      const assets = parseAssetsUsed(transaction.assetsUsed);
      const exchangeRates = parseExchangeRates(transaction.exchangeRates);

      // Determine the main transfer amount and currency
      let transferAmount: number;
      let transferCurrency: string;
      let transferDirection: "in" | "out";

      if (transaction.paidOut && transaction.paidOut > 0) {
        transferAmount = transaction.paidOut * -1;
        transferCurrency = "EUR"; // Assuming EUR as base currency
        transferDirection = "out";
      } else if (transaction.paidIn && transaction.paidIn > 0) {
        transferAmount = transaction.paidIn;
        transferCurrency = "EUR";
        transferDirection = "in";
      } else {
        return err({
          type: "ProcessingError",
          message: "No valid transfer amount found",
        });
      }

      // Create the main transfer
      const transfer = {
        from: transferDirection === "out" ? accountName : "External",
        to: transferDirection === "out" ? "External" : accountName,
        counterparty: transferDirection === "out" ? "External" : accountName,
        amount: new Decimal(transferAmount),
        currencyCode: transferCurrency,
        description: transaction.description,
        executedAt: transaction.timestamp,
      };

      // Create trades from assets and exchange rates
      const trades: ProcessedTransaction["trades"] = [];

      // If we have assets used and exchange rates, create trades
      if (assets.length > 0 && exchangeRates.length > 0) {
        for (const asset of assets) {
          // Find corresponding exchange rate
          const rate = exchangeRates.find(
            (r) => (r.fromCurrency === "EUR" && r.toCurrency === asset.currency) || (r.fromCurrency === asset.currency && r.toCurrency === "EUR")
          );

          if (rate) {
            let tokenFrom: string;
            let tokenTo: string;
            let amountFrom: number;
            let amountTo: number;

            if (transferDirection === "out") {
              // Spending EUR to get crypto
              tokenFrom = asset.currency;
              tokenTo = "EUR";
              amountFrom = transferAmount;
              amountTo = asset.amount;
            } else {
              // Converting crypto to EUR
              tokenFrom = "EUR";
              tokenTo = asset.currency;
              amountFrom = asset.amount;
              amountTo = transferAmount;
            }

            trades.push({
              tokenFrom,
              tokenTo,
              amountFrom: new Decimal(amountFrom),
              amountTo: new Decimal(amountTo),
              description: `${transaction.description} - ${tokenFrom} to ${tokenTo}`,
              poolId: "binance-card",
              executedAt: transaction.timestamp,
            });
          }
        }
      }

      return ok({
        transfer,
        trades,
      });
    } catch (error) {
      return err({
        type: "ProcessingError",
        message: error instanceof Error ? error.message : "Unknown processing error",
      });
    }
  }

  export function processBinanceTransactions(
    transactions: csvUtils.BinanceTransaction[],
    accountName: string
  ): Result<ProcessedTransaction[], { type: "ProcessingError"; message: string; failedTransactions: number }> {
    console.log(`🔍 Processing ${transactions.length} Binance transactions for account: ${accountName}`);

    const processedTransactions: ProcessedTransaction[] = [];
    let failedCount = 0;

    for (let i = 0; i < transactions.length; i++) {
      const transaction = transactions[i];
      const result = processBinanceTransaction(transaction, accountName);
      if (result.isOk()) {
        processedTransactions.push(result.value);
      } else {
        failedCount++;
        console.warn(`⚠️ Transaction ${i + 1} failed to process: ${transaction.description}`, result.error);
      }
    }

    console.log(`✅ Processed ${processedTransactions.length} transactions successfully, ${failedCount} failed`);

    if (failedCount > 0 && processedTransactions.length === 0) {
      return err({
        type: "ProcessingError",
        message: `Failed to process all ${failedCount} transactions`,
        failedTransactions: failedCount,
      });
    }

    return ok(processedTransactions);
  }
}
