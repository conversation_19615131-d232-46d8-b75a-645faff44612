import { fuzzy } from "fast-fuzzy";
const DIGIT_RUN = /\d{4,}/g; // long numeric junk
const URL_CHUNK = /https?:\/\/|\.[a-z]{2,3}\/.*$/; // kill ".com/foo"
const NON_ALNUM = /[^a-z0-9]+/g;

/** Aggressive normalisation so embeddings see only vendor-relevant tokens. */
export function normalise1(raw: string): string {
  return raw
    .normalize("NFKD") // strip accents
    .replace(DIGIT_RUN, " ")
    .replace(URL_CHUNK, " ")
    .toLowerCase()
    .replace(NON_ALNUM, " ")
    .trim()
    .replace(/\s{2,}/g, " "); // collapse whitespace
}

export namespace textUtils {
  export function fuzzyMatch(a: string, b: string) {
    return fuzzy(normalise1(a), normalise1(b));
  }

  export function normalize(str: string): string {
    return str
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, " ")
      .trim();
  }

  export function levenshtein(a: string, b: string): number {
    const aLen = a.length;
    const bLen = b.length;
    if (aLen === 0) return bLen;
    if (bLen === 0) return aLen;

    const matrix: number[][] = Array.from({ length: aLen + 1 }, () => new Array(bLen + 1).fill(0));

    for (let i = 0; i <= aLen; i++) matrix[i][0] = i;
    for (let j = 0; j <= bLen; j++) matrix[0][j] = j;

    for (let i = 1; i <= aLen; i++) {
      for (let j = 1; j <= bLen; j++) {
        const cost = a[i - 1] === b[j - 1] ? 0 : 1;
        matrix[i][j] = Math.min(matrix[i - 1][j] + 1, matrix[i][j - 1] + 1, matrix[i - 1][j - 1] + cost);
      }
    }

    return matrix[aLen][bLen];
  }

  export function similarity(a: string, b: string): number {
    const normA = normalize(a);
    const normB = normalize(b);
    if (!normA || !normB) return 0;
    if (normB.includes(normA)) return 1;

    const distance = levenshtein(normA, normB);
    return 1 - distance / Math.max(normA.length, normB.length);
  }

  const COMPANY_SUFFIXES = new Set(["ltd", "limited", "llc", "gmbh", "inc", "sa", "bv", "ag", "oy", "srl"]);

  export function tokenizeVendor(str: string): string[] {
    return normalize(str)
      .split(/\s+/)
      .flatMap((t) => t.split("_"))
      .filter(Boolean)
      .filter((t) => !COMPANY_SUFFIXES.has(t));
  }

  export function vendorNameSimilarity(a: string, b: string): number {
    const aTokens = tokenizeVendor(a);
    const bTokens = tokenizeVendor(b);
    if (!aTokens.length || !bTokens.length) return 0;
    const longer = Math.max(aTokens.length, bTokens.length);
    let total = 0;
    for (const at of aTokens) {
      let best = 0;
      for (const bt of bTokens) {
        const sim = similarity(at, bt);
        if (sim > best) best = sim;
      }
      total += best;
    }
    return total / longer;
  }
}


if (require.main === module) {
  (async () => {
    const score = await textUtils.similarity("Upwork Global Inc.", "Upwork -469080486REF Upwork.com/biIE");
    console.log("Similarity:", score.toFixed(3));
  })().catch((err) => {
    console.error(err);
    process.exit(1);
  });
}