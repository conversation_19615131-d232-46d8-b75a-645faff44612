import <PERSON> from "papaparse";
import { Result, ok, err } from "neverthrow";
import { Decimal } from "@/prisma/generated/runtime/library";
import { z } from "zod";

import { motlLlm } from "@/modules/ai/llm/motlLlm";
import { parse } from "date-fns";
import { zodUtils } from "./zodUtils";

export namespace csvUtils {
  export interface ParsedCSV {
    data: Record<string, any>[];
    headers: string[];
    rowCount: number;
    preview: Record<string, any>[];
  }

  export function parseCSV(csvContent: string): Result<ParsedCSV, { type: "CSVParseError"; message: string; error: any }> {
    try {
      console.log(`🔍 Parsing CSV content, length: ${csvContent.length} characters`);

      const parseResult = Papa.parse(csvContent, {
        header: true,
        skipEmptyLines: true,
        transformHeader: (header: string) => header.trim(),
        transform: (value: string) => value.trim(),
      });

      console.log(`📊 Papa Parse result: ${parseResult.data.length} rows, ${parseResult.errors.length} errors`);

      if (parseResult.errors.length > 0) {
        console.warn("⚠️ Papa Parse errors:", parseResult.errors);
        // Don't fail on errors, just log them - some errors might be non-critical
        if (parseResult.errors.some((e) => e.type === "Delimiter")) {
          return err({
            type: "CSVParseError",
            message: `CSV parsing errors: ${parseResult.errors.map((e) => e.message).join(", ")}`,
            error: parseResult.errors,
          });
        }
      }

      const data = parseResult.data as Record<string, any>[];
      const headers = parseResult.meta.fields || [];
      const preview = data.slice(0, 5); // First 5 rows for preview

      console.log(`✅ CSV parsed successfully: ${data.length} rows, headers: ${headers.join(", ")}`);

      return ok({
        data,
        headers,
        rowCount: data.length,
        preview,
      });
    } catch (error) {
      console.error("❌ CSV parsing failed:", error);
      return err({
        type: "CSVParseError",
        message: error instanceof Error ? error.message : "Unknown CSV parsing error",
        error,
      });
    }
  }

  export function analyzeCSVStructure(parsed: ParsedCSV): {
    summary: string;
    columnTypes: Record<string, string>;
    sampleData: string;
  } {
    const { data, headers } = parsed;

    // Analyze column types
    const columnTypes: Record<string, string> = {};
    headers.forEach((header) => {
      const sampleValues = data
        .slice(0, 10)
        .map((row) => row[header])
        .filter((val) => val !== null && val !== undefined && val !== "");

      if (sampleValues.length === 0) {
        columnTypes[header] = "empty";
        return;
      }

      // Check if all values are numbers
      const allNumbers = sampleValues.every((val) => !isNaN(Number(val)) && val !== "");
      if (allNumbers) {
        columnTypes[header] = "number";
        return;
      }

      // Check if all values are dates
      const allDates = sampleValues.every((val) => !isNaN(Date.parse(val)));
      if (allDates) {
        columnTypes[header] = "date";
        return;
      }

      columnTypes[header] = "text";
    });

    const summary = `CSV contains ${parsed.rowCount} rows and ${headers.length} columns: ${headers.join(", ")}`;
    const sampleData = JSON.stringify(parsed.preview, null, 2);

    return {
      summary,
      columnTypes,
      sampleData,
    };
  }

  // Binance-specific CSV processing
  export interface BinanceTransaction {
    timestamp: Date;
    description: string;
    paidOut?: number;
    paidIn?: number;
    transactionFee: number;
    assetsUsed: string;
    exchangeRates: string;
  }

  export interface ParsedAsset {
    currency: string;
    amount: number;
  }

  export interface ParsedExchangeRate {
    fromCurrency: string;
    toCurrency: string;
    rate: number;
  }

  export interface ProcessedTransaction {
    transfer: {
      from: string;
      to: string;
      amount: Decimal;
      currencyCode: string;
      description: string;
      executedAt: Date;
    };
    trades: {
      tokenFrom: string;
      tokenTo: string;
      amountFrom: Decimal;
      amountTo: Decimal;
      description: string;
      poolId: string;
      executedAt: Date;
    }[];
  }

  export function parseBinanceCSV(csvContent: string): Result<BinanceTransaction[], { type: "BinanceParseError"; message: string; error: any }> {
    console.log("🔍 Starting Binance CSV parsing...");

    const parseResult = parseCSV(csvContent);
    if (parseResult.isErr()) {
      console.error("❌ Basic CSV parsing failed:", parseResult.error);
      return err({
        type: "BinanceParseError",
        message: parseResult.error.message,
        error: parseResult.error,
      });
    }

    console.log(`📊 Basic CSV parsing successful: ${parseResult.value.data.length} rows`);

    try {
      const transactions: BinanceTransaction[] = [];
      let failedRows = 0;

      for (let i = 0; i < parseResult.value.data.length; i++) {
        const row = parseResult.value.data[i];

        try {
          const timestamp = new Date(row.Timestamp);
          if (isNaN(timestamp.getTime())) {
            console.warn(`⚠️ Row ${i + 1}: Invalid timestamp: ${row.Timestamp}`);
            failedRows++;
            continue;
          }

          // Helper function to parse currency values (removes € symbol and other non-numeric characters)
          const parseCurrency = (value: string): number | undefined => {
            if (!value || value.trim() === "") return undefined;
            // Remove currency symbols, spaces, and other non-numeric characters except decimal point and minus
            const cleanValue = value.replace(/[€$£¥,\s]/g, "").replace(/[^\d.-]/g, "");
            const parsed = parseFloat(cleanValue);
            return isNaN(parsed) ? undefined : parsed;
          };

          const transaction = {
            timestamp,
            description: row.Description || "",
            paidOut: parseCurrency(row["Paid OUT (EUR)"]),
            paidIn: parseCurrency(row["Paid IN (EUR)"]),
            transactionFee: parseCurrency(row["Transaction Fee (EUR)"]) || 0,
            assetsUsed: row["Assets Used"] || "",
            exchangeRates: row["Exchange Rates"] || "",
          };

          transactions.push(transaction);
        } catch (rowError) {
          console.warn(`⚠️ Row ${i + 1}: Failed to process:`, rowError, "Row data:", row);
          failedRows++;
        }
      }

      console.log(`✅ Binance CSV parsing completed: ${transactions.length} transactions, ${failedRows} failed rows`);

      if (failedRows > 0) {
        console.warn(`⚠️ ${failedRows} rows failed to parse but continuing with ${transactions.length} successful transactions`);
      }

      return ok(transactions);
    } catch (error) {
      console.error("❌ Binance CSV parsing failed:", error);
      return err({
        type: "BinanceParseError",
        message: error instanceof Error ? error.message : "Unknown Binance parsing error",
        error,
      });
    }
  }

  export async function parseGenericCSV(csvContent: string) {
    const columnMapping = z.object({
      columnMappings: z.object({
        date: z.string(),
        amount: z.string(),
        currencyCode: zodUtils.optional(z.string()),
        // counterparty: zodUtils.optional(z.string()).describe("The counterparty or other other party that is involved in the transaction."),
        beneficiaryIban: zodUtils.optional(z.string()),
        beneficiaryName: zodUtils.optional(z.string()),
        description: zodUtils.optional(z.string()),
        id: zodUtils.optional(z.string()),
      }),
      dateFormat: z.string().describe("The date format of the csv. This should be in the correct format for the date-fns library parse function."),
      currencyCodeValueForAllRows: zodUtils
        .optional(z.string())
        .describe("The currency code value for all rows. This is used to determine the currency code for all rows."),
    });

    const parseResult = parseCSV(csvContent);

    if (parseResult.isErr()) {
      return err({
        type: "GenericParseError",
        message: parseResult.error.message,
        error: parseResult.error,
      });
    }

    const rows = parseResult.value.data;

    return motlLlm.generateObjectWithValidation({
      aiModel: "o4-mini",
      schema: columnMapping,
      messages: [
        {
          role: "system",
          content: `You are a helpful assistant that gets a csv export with financial transactions and must map the column names of the csv to the column names of our database. 

also map the date format of the csv to the date format of our database.

you also need to provide the date format of the csv.

only map columnes which are cearly the same as the column names in the database. if you are not sure and it' an optional column, then don't map it.
            `,
        },
        {
          role: "user",
          content: csvContent,
        },
      ],
      validateAndTransform(data) {
        data.columnMappings;

        const mappedRows = rows
          .map((row) => {
            return Object.fromEntries(Object.entries(data.columnMappings).map(([key, value]) => [key, value ? row[value] : undefined])) as z.infer<
              typeof columnMapping.shape.columnMappings
            >;
          })
          .map((row) => {
            const ts = parse(row.date, data.dateFormat, new Date());

            return {
              ...row,
              date: ts,
              currencyCode: data.currencyCodeValueForAllRows ? data.currencyCodeValueForAllRows : row.currencyCode,
              externalId: row.id,
              counterpartyIban: row.beneficiaryIban,
              counterparty: row.beneficiaryName,
            };
          });

        return ok({
          data: mappedRows,
        });
      },
    });
  }
}
