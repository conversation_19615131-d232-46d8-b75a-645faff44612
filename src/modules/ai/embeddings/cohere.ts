// vendor-embed-api.ts
// -------------------------------------------------------------
// Compare two vendor strings using Cohere remote embeddings.
// Requires:  - cohere-ai            (v2 SDK)
//           - rapidfuzz             (token_set_ratio)
// Environment: set COHERE_API_KEY.
// -------------------------------------------------------------

import { CohereClientV2, Cohere } from "cohere-ai";
import { fuzzy } from "fast-fuzzy";
import { envVars } from "@/envVars";
import { createMotlCacher } from "@/modules/core/motlCacher";

// ---------- init Cohere client ---------------------------------------------
const cohereClient = new CohereClientV2({
  token: envVars.COHERE_API_KEY, // throws if undefined
});

const cohereHttpClient = createMotlCacher({
  url: "https://api.cohere.ai/v2",
  headers: {
    Authorization: `bearer ${envVars.COHERE_API_KEY}`,
  },
});

// ---------- helpers ----------------------------------------------------------
const DIGIT_RUN = /\d{4,}/g;
const URL_CHUNK = /https?:\/\/|\.[a-z]{2,3}\/.*$/;
const NON_ALNUM = /[^a-z0-9]+/g;

export namespace cohere {
  /** Aggressive cleanup so embeddings see only vendor-relevant tokens. */
  function normalise(raw: string): string {
    return raw
      .normalize("NFKD") // strip accents
      .replace(DIGIT_RUN, " ")
      .replace(URL_CHUNK, " ")
      .toLowerCase()
      .replace(NON_ALNUM, " ")
      .trim()
      .replace(/\s{2,}/g, " ");
  }

  /** Fetch a normalised embedding vector from Cohere. */
  async function embed(text: string) {
    // const res = await cohereClient.embed(
    //   {
    //     inputs: [{ content: [{ type: "text", text }] }],
    //     model: "embed-v4.0",
    //     inputType: "search_query", // good default for short strings
    //     embeddingTypes: ["float"],
    //   },
    //   {}
    // );

    // if (!res.embeddings?.float) {
    //   throw new Error("No embeddings returned from Cohere");
    // }

    const resRaw = await cohereHttpClient.post("embed", {
      json: {
        inputs: [{ content: [{ type: "text", text }] }],
        model: "embed-v4.0",
        input_type: "search_query", // good default for short strings
        embedding_types: ["float"],
      },
    });

    const cacheHit = !!resRaw.headers.get("x-edge-cache");

    if (process.env.DEBUG) console.log("cacheHit", cacheHit);

    const res = await resRaw.json<Cohere.EmbedByTypeResponse>();
    if (!res.embeddings?.float) {
      throw new Error("No embeddings returned from Cohere");
    }

    return res.embeddings.float[0]; // first (and only) vector
  }

  /** Plain cosine similarity (vectors are not unit-normalised by Cohere). */
  function cosine(a: number[], b: number[]): number {
    let dot = 0,
      na = 0,
      nb = 0;
    for (let i = 0; i < a.length; i++) {
      dot += a[i] * b[i];
      na += a[i] * a[i];
      nb += b[i] * b[i];
    }
    return dot / (Math.sqrt(na) * Math.sqrt(nb));
  }

  export async function embedSimilarity(a: string, b: string): Promise<number> {
    const [vecA, vecB] = await Promise.all([embed(normalise(a)), embed(normalise(b))]);
    const cos = cosine(vecA, vecB); // 0 … 1
    return cos;
  }

  export async function fuzzySimilarity(a: string, b: string): Promise<number> {
    const na = normalise(a);
    const nb = normalise(b);
    return fuzzy(na, nb);
  }

  /** Main public API: returns 0 … 1 similarity score. */
  export async function mixedSimilarity(a: string, b: string): Promise<number> {
    const na = normalise(a);
    const nb = normalise(b);

    // fetch embeddings in parallel
    const [vecA, vecB] = await Promise.all([embed(a), embed(b)]);
    const cos = cosine(vecA, vecB); // 0 … 1

    const fuzzScore = fuzzy(na, nb);
    return 0.6 * cos + 0.4 * fuzzScore; // weighted blend
  }
}

// ---------- CLI demo ---------------------------------------------------------
if (require.main === module) {
  (async () => {
    async function testScore(a: string, b: string) {
      const score = await cohere.embedSimilarity(a, b);
      console.log(`similarity: "${a}" vs "${b}": ${score.toFixed(3)}`);

      const fuzzyScore = await cohere.fuzzySimilarity(a, b);
      console.log(`fuzzy similarity: "${a}" vs "${b}": ${fuzzyScore.toFixed(3)}`);

      const mixedScore = await cohere.mixedSimilarity(a, b);
      console.log(`mixed similarity: "${a}" vs "${b}": ${mixedScore.toFixed(3)}`);
    }

    await testScore("Upwork Global Inc.", "Upwork -469080486REF Upwork.com/biIE");
    await testScore("Uber Austria GmbH", "Burger King Wien");
  })().catch((err) => {
    console.error(err);
    process.exit(1);
  });
}
