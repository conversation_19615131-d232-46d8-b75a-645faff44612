// vendor-embed.ts
// -------------------------------------------------------------
// Lightweight vendor-name matcher using local MiniLM embeddings.
// Requires:  - @xenova/transformers  (≈ 130 MB model auto-cached)
//           - rapidfuzz              (fast token_set_ratio)
//
// Usage:
//   const score = await vendorSimilarity("Upwork Global",
//                                        "Upwork -469080486REF Upwork.com/biIE");
//   console.log(score);   // e.g. 0.86  (1.0 = perfect match)
// -------------------------------------------------------------

import { pipeline } from "@xenova/transformers";
import { fuzzy } from "fast-fuzzy";

// ---------- text utils -------------------------------------------------------

const DIGIT_RUN = /\d{4,}/g; // long numeric junk
const URL_CHUNK = /https?:\/\/|\.[a-z]{2,3}\/.*$/; // kill ".com/foo"
const NON_ALNUM = /[^a-z0-9]+/g;

/** Aggressive normalisation so embeddings see only vendor-relevant tokens. */
export function normalise(raw: string): string {
  return raw
    .normalize("NFKD") // strip accents
    .replace(DIGIT_RUN, " ")
    .replace(URL_CHUNK, " ")
    .toLowerCase()
    .replace(NON_ALNUM, " ")
    .trim()
    .replace(/\s{2,}/g, " "); // collapse whitespace
}

// ---------- embedding pipeline ----------------------------------------------

/**
 * Lazy-loads the MiniLM encoder on first use and keeps it in memory.
 * Pooling = mean, normalise = true → 384-d unit vector.
 */
const encoder = (async () => {
  return pipeline("feature-extraction", "Xenova/all-MiniLM-L6-v2", {
    // MiniLM is tiny enough for CPU inference; GPU use is automatic when present.
    quantized: false,
  });
})();

/** Embed a piece of text into a Float32Array (length 384). */
export async function embed(text: string): Promise<Float32Array> {
  const extractor = await encoder;
  const output = await extractor(text, { pooling: "mean", normalize: true });
  return output.data as Float32Array;
}

// ---------- similarity metrics ----------------------------------------------

/** Plain cosine similarity for two unit vectors. */
function cosine(a: Float32Array, b: Float32Array): number {
  let dot = 0;
  for (let i = 0; i < a.length; i++) dot += a[i] * b[i];
  return dot; // norms are 1 by construction
}

/**
 * Combined score = 0.6 × embedding-cosine + 0.4 × RapidFuzz token_set_ratio.
 * Feel free to tweak the weights for your corpus.
 */
export async function vendorSimilarity(a: string, b: string): Promise<number> {
  const na = normalise(a);
  const nb = normalise(b);

  const [vecA, vecB] = await Promise.all([embed(na), embed(nb)]);
  const cos = cosine(vecA, vecB); // 0 … 1

  const fuzzScore = fuzz.token_set_ratio(na, nb) / 100; // 0 … 1

  return 0.6 * cos + 0.4 * fuzzScore;
}

// ---------- quick CLI demo ---------------------------------------------------

if (require.main === module) {
  (async () => {
    const score = await vendorSimilarity("Upwork Global I", "Upwork -469080486REF Upwork.com/biIE");
    console.log("Similarity:", score.toFixed(3));
  })();
}
