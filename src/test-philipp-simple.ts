import fs from "fs";
import path from "path";
import { csvUtils } from "./modules/core/utils/csvUtils";

async function testPhilippSimple() {
  console.log("🔍 Testing Philipp's CSV with our parsing functions...");
  
  try {
    const csvPath = path.join(__dirname, "../data-ai/philipp-binance - Sheet1.csv");
    const csvContent = fs.readFileSync(csvPath, 'utf-8');
    
    console.log(`📁 File: ${csvContent.length} characters`);
    
    // Test our parseCSV function
    const parseResult = csvUtils.parseCSV(csvContent);
    if (parseResult.isErr()) {
      console.error("❌ parseCSV failed:", parseResult.error);
      return;
    }
    
    console.log(`✅ parseCSV: ${parseResult.value.rowCount} rows`);
    
    // Test Binance parsing
    const binanceResult = csvUtils.parseBinanceCSV(csvContent);
    if (binanceResult.isErr()) {
      console.error("❌ parseBinanceCSV failed:", binanceResult.error);
      return;
    }
    
    console.log(`✅ parseBinanceCSV: ${binanceResult.value.length} transactions`);
    
    // Check if there are any failed transactions during processing
    let successCount = 0;
    let failCount = 0;
    
    for (const tx of binanceResult.value) {
      try {
        // Basic validation
        if (isNaN(tx.timestamp.getTime())) {
          failCount++;
          console.warn(`⚠️ Invalid timestamp: ${tx.description}`);
        } else if (!tx.paidOut && !tx.paidIn) {
          failCount++;
          console.warn(`⚠️ No amount: ${tx.description}`);
        } else {
          successCount++;
        }
      } catch (error) {
        failCount++;
        console.warn(`⚠️ Processing error: ${tx.description}`, error);
      }
    }
    
    console.log(`📊 Validation: ${successCount} valid, ${failCount} invalid transactions`);
    
  } catch (error) {
    console.error("❌ Test failed:", error);
  }
}

testPhilippSimple();
