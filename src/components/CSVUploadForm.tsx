import React, { useState } from "react";
import { useForm } from "@tanstack/react-form";
import { trpc } from "@/utils/trpc";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { AccountType } from "@/prisma/generated";
import { toast } from "sonner";
import { Upload, FileText, Loader2 } from "lucide-react";

interface CSVUploadFormProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess?: () => void;
  preselectedAccountId?: string;
}

interface UploadedFile {
  file: File;
  content: string;
  analysis?: any;
  preview?: any;
}

export function CSVUploadForm({ open, onOpenChange, onSuccess, preselectedAccountId }: CSVUploadFormProps) {
  const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([]);
  const [currentStep, setCurrentStep] = useState<"upload" | "configure" | "preview" | "processing">("upload");
  const [selectedAccountId, setSelectedAccountId] = useState<string>(preselectedAccountId || "");
  const [createNewAccount, setCreateNewAccount] = useState(false);

  // Fetch accounts for selection
  const { data: accountsData } = trpc.accounts.getAll.useQuery({ limit: 100 });
  const accounts = accountsData?.items || [];

  // Update selected account when preselected account changes
  React.useEffect(() => {
    if (preselectedAccountId) {
      setSelectedAccountId(preselectedAccountId);
      setCreateNewAccount(false);
    }
  }, [preselectedAccountId]);

  const form = useForm({
    defaultValues: {
      newAccountName: "",
      newAccountType: "CREDIT_CARD" as AccountType,
    },
    onSubmit: async ({ value }) => {
      if (uploadedFiles.length === 0) {
        toast.error("Please upload at least one CSV file");
        return;
      }

      if (!selectedAccountId && !createNewAccount) {
        toast.error("Please select an account or choose to create a new one");
        return;
      }

      if (createNewAccount) {
        if (!value.newAccountName || value.newAccountName.trim().length === 0) {
          toast.error("Please enter a name for the new account");
          return;
        }
      }

      setCurrentStep("processing");

      try {
        for (const uploadedFile of uploadedFiles) {
          await processCSVMutation.mutateAsync({
            fileContent: uploadedFile.content,
            csvType: uploadedFile.analysis?.detectedType || "binance-card",
            accountId: createNewAccount ? "" : selectedAccountId,
            createNewAccount,
            newAccountName: createNewAccount ? value.newAccountName : undefined,
            newAccountType: createNewAccount ? value.newAccountType : undefined,
          });
        }

        toast.success(`Successfully imported ${uploadedFiles.length} CSV file(s)`);
        onOpenChange(false);
        resetForm();
        onSuccess?.();
      } catch (error) {
        console.error("Import failed:", error);
        setCurrentStep("preview");
      }
    },
  });

  const analyzeCSVMutation = trpc.csv.uploadAndAnalyze.useMutation({
    onSuccess: (data, variables) => {
      console.log(`✅ Analysis successful for ${variables.fileName}: ${data.rowCount} rows detected`);
      const fileIndex = uploadedFiles.findIndex((f) => f.file.name === variables.fileName);
      if (fileIndex >= 0) {
        setUploadedFiles((prev) => prev.map((f, i) => (i === fileIndex ? { ...f, analysis: data } : f)));
      }
    },
    onError: (error) => {
      console.error(`❌ Analysis failed:`, error);
      toast.error(`Analysis failed: ${error.message}`);
    },
  });

  const previewMutation = trpc.csv.previewTransactions.useMutation({
    onSuccess: (data, variables) => {
      const fileIndex = uploadedFiles.findIndex((f) => f.content === variables.fileContent);
      if (fileIndex >= 0) {
        setUploadedFiles((prev) => prev.map((f, i) => (i === fileIndex ? { ...f, preview: data } : f)));
      }
    },
    onError: (error) => {
      toast.error(`Preview failed: ${error.message}`);
    },
  });

  const processCSVMutation = trpc.csv.processAndImport.useMutation({
    onError: (error) => {
      toast.error(`Import failed: ${error.message}`);
    },
  });

  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (!files || files.length === 0) return;

    const newFiles: UploadedFile[] = [];

    for (const file of Array.from(files)) {
      if (!file.name.toLowerCase().endsWith(".csv") && file.type !== "text/csv") {
        toast.error(`${file.name} is not a CSV file`);
        continue;
      }

      try {
        const content = await file.text();
        console.log(`📁 File ${file.name}: ${content.length} characters, ${file.size} bytes`);

        const uploadedFile: UploadedFile = { file, content };
        newFiles.push(uploadedFile);

        // Analyze the CSV
        analyzeCSVMutation.mutate({
          fileContent: content,
          fileName: file.name,
          csvType: "binance-card", // Default, will be detected
        });
      } catch (error) {
        console.error(`Failed to read ${file.name}:`, error);
        toast.error(`Failed to read ${file.name}`);
      }
    }

    setUploadedFiles((prev) => [...prev, ...newFiles]);
    if (newFiles.length > 0) {
      setCurrentStep("configure");
    }
  };

  const handlePreview = async () => {
    if (!selectedAccountId && !createNewAccount) {
      toast.error("Please select an account or choose to create a new one");
      return;
    }

    if (createNewAccount) {
      const newAccountName = form.getFieldValue("newAccountName");
      if (!newAccountName || newAccountName.trim().length === 0) {
        toast.error("Please enter a name for the new account");
        return;
      }
    }

    const accountName = createNewAccount ? form.getFieldValue("newAccountName") : accounts.find((a) => a.id === selectedAccountId)?.name || "Unknown";

    for (const uploadedFile of uploadedFiles) {
      if (uploadedFile.analysis) {
        previewMutation.mutate({
          fileContent: uploadedFile.content,
          csvType: uploadedFile.analysis.detectedType,
          accountName,
        });
      }
    }

    setCurrentStep("preview");
  };

  const resetForm = () => {
    setUploadedFiles([]);
    setCurrentStep("upload");
    setSelectedAccountId("");
    setCreateNewAccount(false);
    form.reset();
  };

  const isLoading = analyzeCSVMutation.isPending || previewMutation.isPending || processCSVMutation.isPending;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[800px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Upload CSV Files</DialogTitle>
          <DialogDescription>Upload multiple CSV files to import transactions. Currently supports Binance credit card transaction files.</DialogDescription>
        </DialogHeader>

        {currentStep === "upload" && (
          <div className="space-y-4">
            <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center">
              <input type="file" multiple accept=".csv,text/csv" onChange={handleFileUpload} className="hidden" id="csv-upload" />
              <label htmlFor="csv-upload" className="cursor-pointer flex flex-col items-center gap-2">
                <Upload className="h-12 w-12 text-gray-400" />
                <div>
                  <p className="text-lg font-medium">Upload CSV Files</p>
                  <p className="text-sm text-gray-500">Click to select multiple CSV files</p>
                </div>
              </label>
            </div>
          </div>
        )}

        {currentStep === "configure" && (
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-medium mb-4">Uploaded Files</h3>
              <div className="space-y-2">
                {uploadedFiles.map((uploadedFile, index) => (
                  <Card key={index}>
                    <CardContent className="p-4">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <FileText className="h-4 w-4" />
                          <span className="font-medium">{uploadedFile.file.name}</span>
                          {uploadedFile.analysis && (
                            <Badge variant={uploadedFile.analysis.detectedType === "binance-card" ? "default" : "secondary"}>
                              {uploadedFile.analysis.detectedType}
                            </Badge>
                          )}
                        </div>
                        {uploadedFile.analysis && <span className="text-sm text-gray-500">{uploadedFile.analysis.rowCount} rows</span>}
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>

            <Separator />

            <div>
              <h3 className="text-lg font-medium mb-4">Account Configuration</h3>
              <div className="space-y-4">
                <div className="flex items-center space-x-2">
                  <input type="radio" id="existing-account" name="account-option" checked={!createNewAccount} onChange={() => setCreateNewAccount(false)} />
                  <Label htmlFor="existing-account">Use existing account</Label>
                </div>

                {!createNewAccount && (
                  <Select value={selectedAccountId} onValueChange={setSelectedAccountId}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select an account" />
                    </SelectTrigger>
                    <SelectContent>
                      {accounts.map((account) => (
                        <SelectItem key={account.id} value={account.id}>
                          {account.name} ({account.type})
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                )}

                <div className="flex items-center space-x-2">
                  <input type="radio" id="new-account" name="account-option" checked={createNewAccount} onChange={() => setCreateNewAccount(true)} />
                  <Label htmlFor="new-account">Create new account</Label>
                </div>

                {createNewAccount && (
                  <div className="space-y-4 ml-6">
                    <form.Field name="newAccountName">
                      {(field) => (
                        <div>
                          <Label htmlFor={field.name}>Account Name</Label>
                          <Input
                            id={field.name}
                            value={field.state.value}
                            onChange={(e) => field.handleChange(e.target.value)}
                            placeholder="Enter account name"
                          />
                        </div>
                      )}
                    </form.Field>

                    <form.Field name="newAccountType">
                      {(field) => (
                        <div>
                          <Label htmlFor={field.name}>Account Type</Label>
                          <Select value={field.state.value} onValueChange={(value) => field.handleChange(value as AccountType)}>
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="CREDIT_CARD">Credit Card</SelectItem>
                              <SelectItem value="BANK_ACCOUNT">Bank Account</SelectItem>
                              <SelectItem value="EXCHANGE_ACCOUNT">Exchange Account</SelectItem>
                              <SelectItem value="WALLET">Wallet</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                      )}
                    </form.Field>
                  </div>
                )}
              </div>
            </div>
          </div>
        )}

        {currentStep === "preview" && (
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Import Preview</h3>
            {uploadedFiles.map((uploadedFile, index) => (
              <Card key={index}>
                <CardHeader>
                  <CardTitle className="text-base">{uploadedFile.file.name}</CardTitle>
                  {uploadedFile.preview && (
                    <CardDescription>
                      {uploadedFile.preview.totalTransactions} transactions, {uploadedFile.preview.totalTransfers} transfers, {uploadedFile.preview.totalTrades}{" "}
                      trades
                    </CardDescription>
                  )}
                </CardHeader>
                {uploadedFile.preview && (
                  <CardContent>
                    <div className="text-sm space-y-2">
                      <p>
                        <strong>Total Amount:</strong> €{uploadedFile.preview.summary.totalAmount.toFixed(2)}
                      </p>
                      <p>
                        <strong>Currencies:</strong> {uploadedFile.preview.summary.currencies.join(", ")}
                      </p>
                      <p>
                        <strong>Date Range:</strong> {new Date(uploadedFile.preview.summary.dateRange.from).toLocaleDateString()} -{" "}
                        {new Date(uploadedFile.preview.summary.dateRange.to).toLocaleDateString()}
                      </p>
                    </div>
                  </CardContent>
                )}
              </Card>
            ))}
          </div>
        )}

        {currentStep === "processing" && (
          <div className="flex flex-col items-center justify-center py-8">
            <Loader2 className="h-8 w-8 animate-spin mb-4" />
            <p className="text-lg font-medium">Processing CSV files...</p>
            <p className="text-sm text-gray-500">This may take a few moments</p>
          </div>
        )}

        <DialogFooter>
          {currentStep === "upload" && (
            <Button onClick={() => onOpenChange(false)} variant="outline">
              Cancel
            </Button>
          )}

          {currentStep === "configure" && (
            <>
              <Button onClick={resetForm} variant="outline">
                Start Over
              </Button>
              <Button onClick={handlePreview} disabled={isLoading}>
                Preview Import
              </Button>
            </>
          )}

          {currentStep === "preview" && (
            <>
              <Button onClick={() => setCurrentStep("configure")} variant="outline">
                Back
              </Button>
              <Button onClick={() => form.handleSubmit()} disabled={isLoading}>
                Import Transactions
              </Button>
            </>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
