import { useState } from "react";
import { <PERSON><PERSON>, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import { SearchInput } from "@/components/SearchInput";
import { trpc } from "@/utils/trpc";
import { Search, Link, ExternalLink, Calendar, DollarSign, Building2, Loader2 } from "lucide-react";
import { currency } from "@/modules/core/currency";
import { toast } from "sonner";

interface TransferSearchDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  invoiceId: string;
  onMatchSuccess?: () => void;
}

export function TransferSearchDialog({ open, onOpenChange, invoiceId, onMatchSuccess }: TransferSearchDialogProps) {
  const [searchTerm, setSearchTerm] = useState("charges");
  const [selectedTransferId, setSelectedTransferId] = useState<string | null>(null);

  // Search transfers
  const {
    data: transfers,
    isLoading: searchLoading,
    refetch,
  } = trpc.transfers.search.useQuery(
    {
      search: searchTerm,
      limit: 20,
      excludeReconciled: true,
    },
    {
      enabled: !!searchTerm && searchTerm.length > 0,
    }
  );

  // Manual match mutation
  const manualMatchMutation = trpc.reconciliation.manualMatch.useMutation({
    onSuccess: () => {
      toast.success("Transfer matched successfully!");
      onMatchSuccess?.();
      onOpenChange(false);
      setSelectedTransferId(null);
    },
    onError: (error) => {
      toast.error(`Failed to match transfer: ${error.message}`);
    },
  });

  const handleMatch = (transferId: string) => {
    setSelectedTransferId(transferId);
    manualMatchMutation.mutate({
      invoiceId,
      transferId,
    });
  };

  const handleSearchChange = (value: string) => {
    setSearchTerm(value);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-hidden flex flex-col">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Search className="h-5 w-5" />
            Search Transfers to Match
          </DialogTitle>
          <DialogDescription>Search for transfers by description or counterparty to manually match with this invoice.</DialogDescription>
        </DialogHeader>

        <div className="space-y-4 flex-1 overflow-hidden flex flex-col">
          {/* Search Input */}
          <div className="space-y-2">
            <label className="text-sm font-medium">Search Term</label>
            <SearchInput value={searchTerm} onChange={handleSearchChange} placeholder="Search transfers by description or counterparty..." className="w-full" />
          </div>

          {/* Results */}
          <div className="flex-1 overflow-y-auto space-y-3">
            {searchLoading && (
              <div className="flex items-center justify-center py-8">
                <Loader2 className="h-6 w-6 animate-spin" />
                <span className="ml-2">Searching transfers...</span>
              </div>
            )}

            {!searchLoading && searchTerm && transfers && transfers.length === 0 && (
              <div className="text-center py-8 text-muted-foreground">No transfers found matching "{searchTerm}"</div>
            )}

            {!searchLoading && transfers && transfers.length > 0 && (
              <div className="space-y-3">
                <div className="text-sm text-muted-foreground">
                  Found {transfers.length} transfer{transfers.length !== 1 ? "s" : ""}
                </div>
                {transfers.map((transfer) => (
                  <Card key={transfer.id} className="hover:bg-muted/50 transition-colors">
                    <CardContent className="p-4">
                      <div className="flex items-center justify-between">
                        <div className="flex-1 space-y-2">
                          {/* Amount and Date */}
                          <div className="flex items-center gap-4">
                            <div className="flex items-center gap-1">
                              <DollarSign className="h-4 w-4 text-green-600" />
                              <span className="font-medium">{currency.formatMonetary(transfer.amount, transfer.currencyCode)}</span>
                            </div>
                            <div className="flex items-center gap-1 text-sm text-muted-foreground">
                              <Calendar className="h-4 w-4" />
                              {new Date(transfer.transaction.executedAt).toLocaleDateString()}
                            </div>
                          </div>

                          {/* Description */}
                          {transfer.description && (
                            <div className="text-sm">
                              <span className="font-medium">Description:</span> {transfer.description}
                            </div>
                          )}

                          {/* Counterparty */}
                          {transfer.counterparty && (
                            <div className="flex items-center gap-1 text-sm">
                              <Building2 className="h-4 w-4" />
                              <span className="font-medium">Counterparty:</span> {transfer.counterparty}
                            </div>
                          )}

                          {/* Account */}
                          <div className="text-sm text-muted-foreground">Account: {transfer.transaction.account.name}</div>

                          {/* Reconciliation Status */}
                          {transfer.reconciliations.length > 0 ? (
                            <Badge variant="secondary">Already Reconciled</Badge>
                          ) : (
                            <Badge variant="outline">Unmatched</Badge>
                          )}
                        </div>

                        {/* Actions */}
                        <div className="flex items-center gap-2 ml-4">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => window.open(`/transactions?transactionId=${transfer.transaction.id}`, "_blank")}
                            className="flex items-center gap-1"
                          >
                            <ExternalLink className="h-3 w-3" />
                            View
                          </Button>

                          {transfer.reconciliations.length === 0 && (
                            <Button
                              size="sm"
                              onClick={() => handleMatch(transfer.id)}
                              disabled={manualMatchMutation.isPending && selectedTransferId === transfer.id}
                              className="flex items-center gap-1"
                            >
                              {manualMatchMutation.isPending && selectedTransferId === transfer.id ? (
                                <Loader2 className="h-3 w-3 animate-spin" />
                              ) : (
                                <Link className="h-3 w-3" />
                              )}
                              Match
                            </Button>
                          )}
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}

            {!searchTerm && <div className="text-center py-8 text-muted-foreground">Enter a search term to find transfers</div>}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
