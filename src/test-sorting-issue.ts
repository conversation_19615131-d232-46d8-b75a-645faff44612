import fs from "fs";
import path from "path";
import { csvUtils } from "./modules/core/utils/csvUtils";
import { binanceUtils } from "./modules/core/utils/binanceUtils";

async function testSortingIssue() {
  try {
    console.log("🔍 Testing sorting-related parsing issues...");
    
    // Read the original CSV file
    const csvPath = path.join(__dirname, "../data-ai/binance_card_2022 - sheet1.csv");
    const csvContent = fs.readFileSync(csvPath, 'utf-8');
    
    console.log(`📁 Original file: ${csvContent.length} characters`);
    
    // Parse the original CSV
    const originalParseResult = csvUtils.parseCSV(csvContent);
    if (originalParseResult.isErr()) {
      console.error("❌ Original CSV parsing failed:", originalParseResult.error);
      return;
    }
    
    const originalData = originalParseResult.value.data;
    console.log(`✅ Original parsing: ${originalData.length} rows`);
    
    // Create different sorted versions
    const sortedByDate = [...originalData].sort((a, b) => new Date(a.Timestamp).getTime() - new Date(b.Timestamp).getTime());
    const sortedByDateDesc = [...originalData].sort((a, b) => new Date(b.Timestamp).getTime() - new Date(a.Timestamp).getTime());
    const sortedByDescription = [...originalData].sort((a, b) => (a.Description || "").localeCompare(b.Description || ""));
    
    console.log(`📊 Sorted versions created: ${sortedByDate.length}, ${sortedByDateDesc.length}, ${sortedByDescription.length} rows`);
    
    // Test each sorted version
    const testSortedVersion = async (data: any[], name: string) => {
      console.log(`\n🔍 Testing ${name}...`);
      
      // Convert back to CSV format
      const headers = originalParseResult.value.headers;
      const csvLines = [headers.join(',')];
      
      for (const row of data) {
        const line = headers.map(header => {
          const value = row[header] || '';
          // Escape commas and quotes in CSV values
          if (value.includes(',') || value.includes('"') || value.includes('\n')) {
            return `"${value.replace(/"/g, '""')}"`;
          }
          return value;
        }).join(',');
        csvLines.push(line);
      }
      
      const sortedCsvContent = csvLines.join('\r\n');
      console.log(`📝 ${name} CSV: ${sortedCsvContent.length} characters, ${csvLines.length} lines`);
      
      // Test parsing
      const parseResult = csvUtils.parseCSV(sortedCsvContent);
      if (parseResult.isErr()) {
        console.error(`❌ ${name} parsing failed:`, parseResult.error);
        return false;
      }
      
      console.log(`✅ ${name} parsed: ${parseResult.value.rowCount} rows`);
      
      // Test Binance parsing
      const binanceResult = csvUtils.parseBinanceCSV(sortedCsvContent);
      if (binanceResult.isErr()) {
        console.error(`❌ ${name} Binance parsing failed:`, binanceResult.error);
        return false;
      }
      
      console.log(`✅ ${name} Binance parsed: ${binanceResult.value.length} transactions`);
      
      // Test processing
      const processResult = binanceUtils.processBinanceTransactions(binanceResult.value, "Test Account");
      if (processResult.isErr()) {
        console.error(`❌ ${name} processing failed:`, processResult.error);
        return false;
      }
      
      console.log(`✅ ${name} processed: ${processResult.value.length} transactions`);
      return true;
    };
    
    // Test all versions
    await testSortedVersion(originalData, "Original order");
    await testSortedVersion(sortedByDate, "Date ascending");
    await testSortedVersion(sortedByDateDesc, "Date descending");
    await testSortedVersion(sortedByDescription, "Description alphabetical");
    
    // Test with potential problematic rows
    console.log("\n🔍 Checking for problematic rows...");
    
    for (let i = 0; i < originalData.length; i++) {
      const row = originalData[i];
      
      // Check for empty or invalid timestamps
      if (!row.Timestamp || row.Timestamp.trim() === '') {
        console.warn(`⚠️ Row ${i + 1}: Empty timestamp`);
      }
      
      try {
        const date = new Date(row.Timestamp);
        if (isNaN(date.getTime())) {
          console.warn(`⚠️ Row ${i + 1}: Invalid timestamp: ${row.Timestamp}`);
        }
      } catch (error) {
        console.warn(`⚠️ Row ${i + 1}: Timestamp parsing error: ${row.Timestamp}`, error);
      }
      
      // Check for unusual characters
      const hasUnusualChars = Object.values(row).some(value => 
        typeof value === 'string' && /[\x00-\x08\x0B\x0C\x0E-\x1F\x7F-\x9F]/.test(value)
      );
      
      if (hasUnusualChars) {
        console.warn(`⚠️ Row ${i + 1}: Contains unusual characters`);
      }
    }
    
    // Test truncated versions to simulate the 22-row issue
    console.log("\n🔍 Testing truncated versions...");
    
    const truncatedVersions = [22, 50, 100, 200];
    for (const count of truncatedVersions) {
      const truncatedData = originalData.slice(0, count);
      const success = await testSortedVersion(truncatedData, `First ${count} rows`);
      if (!success) {
        console.error(`❌ Truncated version with ${count} rows failed`);
      }
    }
    
  } catch (error) {
    console.error("❌ Test failed:", error);
  }
}

// Run the test
testSortingIssue().then(() => {
  console.log("\n🎉 Sorting test completed!");
}).catch(error => {
  console.error("💥 Test crashed:", error);
});
