import fs from "fs";
import path from "path";
import { csvUtils } from "./modules/core/utils/csvUtils";
import { binanceUtils } from "./modules/core/utils/binanceUtils";

async function testPhilippCSV() {
  try {
    console.log("🔍 Testing Philipp's Binance CSV file...");
    
    // Read the CSV file
    const csvPath = path.join(__dirname, "../data-ai/philipp-binance - Sheet1.csv");
    const csvContent = fs.readFileSync(csvPath, 'utf-8');
    
    console.log(`📁 File size: ${csvContent.length} characters`);
    
    // Test basic CSV parsing
    console.log("\n🔍 Testing basic CSV parsing...");
    const parseResult = csvUtils.parseCSV(csvContent);
    if (parseResult.isErr()) {
      console.error("❌ Basic CSV parsing failed:", parseResult.error);
      return;
    }
    
    const parsed = parseResult.value;
    console.log(`✅ Basic parsing: ${parsed.rowCount} rows, ${parsed.headers.length} columns`);
    console.log("📋 Headers:", parsed.headers);
    
    // Test Binance CSV parsing
    console.log("\n🔍 Testing Binance CSV parsing...");
    const binanceResult = csvUtils.parseBinanceCSV(csvContent);
    if (binanceResult.isErr()) {
      console.error("❌ Binance CSV parsing failed:", binanceResult.error);
      return;
    }
    
    const transactions = binanceResult.value;
    console.log(`✅ Binance CSV parsed successfully: ${transactions.length} transactions`);
    
    // Test transaction processing
    console.log("\n🔍 Testing transaction processing...");
    const processResult = binanceUtils.processBinanceTransactions(transactions, "Philipp Binance Card");
    if (processResult.isErr()) {
      console.error("❌ Transaction processing failed:", processResult.error);
      return;
    }
    
    const processed = processResult.value;
    console.log(`✅ Processing successful: ${processed.length} transactions`);
    
    // Show some sample transactions
    console.log("\n📝 Sample transactions:");
    const samples = transactions.slice(0, 5);
    samples.forEach((tx, i) => {
      console.log(`  ${i + 1}. ${tx.description} - ${tx.paidOut ? `€${tx.paidOut} OUT` : `€${tx.paidIn} IN`}`);
    });
    
    // Check for any problematic rows
    console.log("\n🔍 Checking for problematic rows...");
    let problematicCount = 0;
    
    for (let i = 0; i < transactions.length; i++) {
      const tx = transactions[i];
      
      // Check for invalid timestamps
      if (isNaN(tx.timestamp.getTime())) {
        console.warn(`⚠️ Row ${i + 1}: Invalid timestamp`);
        problematicCount++;
      }
      
      // Check for missing amounts
      if (!tx.paidOut && !tx.paidIn) {
        console.warn(`⚠️ Row ${i + 1}: No paid amount - ${tx.description}`);
        problematicCount++;
      }
      
      // Check for unusual characters in description
      if (tx.description && /[^\x20-\x7E\u00A0-\u00FF]/.test(tx.description)) {
        console.warn(`⚠️ Row ${i + 1}: Unusual characters in description - ${tx.description}`);
        problematicCount++;
      }
    }
    
    if (problematicCount === 0) {
      console.log("✅ No problematic rows found");
    } else {
      console.log(`⚠️ Found ${problematicCount} potentially problematic rows`);
    }
    
    // Test with different truncation points
    console.log("\n🔍 Testing truncation points...");
    const lines = csvContent.split('\n');
    console.log(`📝 Total lines in file: ${lines.length}`);
    
    const testPoints = [22, 50, 100, 200, 500];
    for (const point of testPoints) {
      if (point < lines.length) {
        const truncatedLines = lines.slice(0, point + 1); // +1 for header
        const truncatedContent = truncatedLines.join('\n');
        
        try {
          const truncatedParseResult = csvUtils.parseCSV(truncatedContent);
          if (truncatedParseResult.isOk()) {
            console.log(`  At line ${point}: ${truncatedParseResult.value.rowCount} rows parsed successfully`);
          } else {
            console.log(`  At line ${point}: Parse failed - ${truncatedParseResult.error.message}`);
          }
        } catch (error) {
          console.log(`  At line ${point}: Exception - ${error.message}`);
        }
      }
    }
    
    // Check line 22 specifically
    console.log("\n🔍 Examining line 22 specifically...");
    if (lines.length > 22) {
      const line22 = lines[22]; // 0-based, so this is actually line 23
      console.log(`Line 22 content: "${line22}"`);
      console.log(`Line 22 length: ${line22.length} characters`);
      
      // Check for unusual characters
      const hasUnusualChars = /[^\x20-\x7E\r\n]/.test(line22);
      console.log(`Has unusual characters: ${hasUnusualChars}`);
      
      if (hasUnusualChars) {
        console.log("Unusual characters found:");
        for (let i = 0; i < line22.length; i++) {
          const char = line22[i];
          const code = char.charCodeAt(0);
          if (code < 0x20 || code > 0x7E) {
            console.log(`  Position ${i}: '${char}' (code: ${code})`);
          }
        }
      }
    }
    
  } catch (error) {
    console.error("❌ Test failed:", error);
  }
}

// Run the test
testPhilippCSV().then(() => {
  console.log("\n🎉 Philipp CSV test completed!");
}).catch(error => {
  console.error("💥 Test crashed:", error);
});
